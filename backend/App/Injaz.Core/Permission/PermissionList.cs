using System.Linq;

namespace Injaz.Core.Permission;

public static class PermissionList
{
    // This contains the list of all permissions, and their
    // "one-level" overriders. For example, the `kpi:read` permission
    // can be overriden by `full_access`, `kpi`, `kpi:write`, or `kpi:delete`
    // permissions. However, only `kpi:write`, and `kpi:delete` are listed.
    // The program will compute the rest recursively on run time.
    private static readonly Permission[] InitialList =
    {
        // Full access
        new(PermissionNameList.FullAccess, System.Array.Empty<string>()),

        // Kpi permissions
        new(PermissionNameList.Kpi, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiRead, new[]
        {
            PermissionNameList.KpiWrite,
            PermissionNameList.KpiDelete,

            // Because they're gonna be able to link kpis to capability,
            // therefore, they should be able to read the kpis.
            PermissionNameList.Capability,

            // the evaluator should have access to kpi ans results,
            PermissionNameList.KpiResultPeriodEvaluate,

            // Because they should be able to read the kpi
            // to read its results.
            PermissionNameList.KpiResult,
            PermissionNameList.KpiResultRead,
            PermissionNameList.KpiResultWrite,
            PermissionNameList.KpiResultEntry,
            PermissionNameList.KpiResultDelete,
            PermissionNameList.KpiResultApprove
        }),
        new(PermissionNameList.KpiWrite, new[] { PermissionNameList.Kpi}),
        new(PermissionNameList.KpiDelete, new[] { PermissionNameList.Kpi }),
        new(PermissionNameList.KpiEvaluate, new[] { PermissionNameList.Kpi
        }),

        // KpiResult permissions
        new(PermissionNameList.KpiResult, new[] { PermissionNameList.FullAccess, PermissionNameList.KpiResultPeriodEvaluate }),
        new(PermissionNameList.KpiResultRead,
            new[]
            {
                PermissionNameList.Kpi,
                PermissionNameList.KpiResultWrite,
                PermissionNameList.KpiResultEntry,
                PermissionNameList.KpiResultDelete,
                PermissionNameList.KpiResultApprove,
                PermissionNameList.KpiResultFlow,
                PermissionNameList.KpiResultDynamic,
                PermissionNameList.KpiResultDirectApprove,
                PermissionNameList.KpiResultResponseApprovalTransfer,
            }),
        new(PermissionNameList.KpiResultWrite, new[] { PermissionNameList.KpiResult }),
        new(PermissionNameList.KpiResultEntry, new[] { PermissionNameList.KpiResultWrite }),
        new(PermissionNameList.KpiResultFlow, new[] { PermissionNameList.KpiResult }),
        new(PermissionNameList.KpiResultDynamic, new[] { PermissionNameList.KpiResultWrite }),
        new(PermissionNameList.KpiResultDynamicApprove, new[] { PermissionNameList.KpiResultApprove }),
        new(PermissionNameList.KpiResultDelete, new[] { PermissionNameList.KpiResult }),
        new(PermissionNameList.KpiResultApprove, new[] { PermissionNameList.KpiResult }),

        // evaluate KPIs results
        new(PermissionNameList.KpiResultPeriodEvaluate, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiResultPeriodEvaluateRead, new[] { PermissionNameList.KpiResultPeriodEvaluateGlobalRead, PermissionNameList.KpiResultRead }),
        new(PermissionNameList.KpiResultPeriodEvaluateGlobalRead, new[] { PermissionNameList.KpiResultPeriodEvaluate, PermissionNameList.KpiResultPeriodExportEvaluation  }),
        new(PermissionNameList.KpiResultPeriodExportEvaluation, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiResultDirectApprove, new[] { PermissionNameList.KpiResult }),
        new(PermissionNameList.KpiResultResponseApprovalTransfer, new[] { PermissionNameList.KpiResult }),

        // Operation permissions
        new(PermissionNameList.Operation, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.OperationRead,
            new[] { PermissionNameList.OperationWrite, PermissionNameList.OperationDelete }),
        new(PermissionNameList.OperationWrite, new[] { PermissionNameList.Operation }),
        new(PermissionNameList.OperationDelete, new[] { PermissionNameList.Operation }),
        new(PermissionNameList.OperationApproveUpdateRequest, new[] { PermissionNameList.Operation }),

        // Plan permissions
        new(PermissionNameList.Plan, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.PlanRead,
            new[]
            {
                PermissionNameList.PlanWrite,
                PermissionNameList.PlanDelete,
                PermissionNameList.PlanInitialApproval,
                PermissionNameList.PlanIntermediateInitialApproval,
                PermissionNameList.PlanSubsubtaskFinalApproval
            }),
        new(PermissionNameList.PlanWrite, new[] { PermissionNameList.Plan }),
        new(PermissionNameList.PlanDelete, new[] { PermissionNameList.Plan }),
        new(
            PermissionNameList.PlanIntermediateInitialApproval,
            new[] { PermissionNameList.PlanImmediateIntermediateInitialApproval }
        ),
        new(PermissionNameList.PlanImmediateIntermediateInitialApproval, new[] { PermissionNameList.Plan }),
        new(PermissionNameList.PlanInitialApproval, new[] { PermissionNameList.Plan }),
        new(PermissionNameList.PlanSubsubtaskFinalApproval, new[] { PermissionNameList.Plan }),

        // Benchmark permissions
        new(PermissionNameList.Benchmark, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.BenchmarkRead,
            new[] { PermissionNameList.BenchmarkWrite, PermissionNameList.BenchmarkDelete }),
        new(PermissionNameList.BenchmarkWrite, new[] { PermissionNameList.Benchmark }),
        new(PermissionNameList.BenchmarkDelete, new[] { PermissionNameList.Benchmark }),

        // User permissions
        new(PermissionNameList.User, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.UserRead, new[] { PermissionNameList.UserWrite, PermissionNameList.UserDelete }),
        new(PermissionNameList.UserWrite, new[] { PermissionNameList.User }),
        new(PermissionNameList.UserDelete, new[] { PermissionNameList.User }),

        // Department permissions
        new(PermissionNameList.Department, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.DepartmentRead,
            new[] { PermissionNameList.DepartmentWrite, PermissionNameList.DepartmentDelete }),
        new(PermissionNameList.DepartmentWrite, new[] { PermissionNameList.Department }),
        new(PermissionNameList.DepartmentDelete, new[] { PermissionNameList.Department }),

        // Reports
        new(PermissionNameList.ReportDepartment, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.ReportKpi, new[] { PermissionNameList.FullAccess }),

        // Partner permissions
        new(PermissionNameList.Partner, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.PartnerExportEvaluation, new[] { PermissionNameList.FullAccess }),
        // Partnership
        new(PermissionNameList.Partnership, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.PartnershipRead,
            new[]
            {
                PermissionNameList.PartnershipWrite,
                PermissionNameList.PartnershipDelete,
                PermissionNameList.PartnershipReviewer
            }),
        new(PermissionNameList.PartnershipWrite, new[] { PermissionNameList.Partnership }),
        new(PermissionNameList.PartnershipDelete, new[] { PermissionNameList.Partnership }),
        new(PermissionNameList.PartnershipReviewer, new[] { PermissionNameList.Partnership }),


        // success factor permissions
        new(PermissionNameList.SuccessFactor, new[] { PermissionNameList.FullAccess }),

        // innovation
        new(PermissionNameList.Innovation, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Innovator, new[] { PermissionNameList.Innovation }),

        // Statistical reports
        new(PermissionNameList.StatisticalReport, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.StatisticalReportDataEntry, new[] { PermissionNameList.StatisticalReport }),
        new(PermissionNameList.StatisticalReportRead,
            new[]
            {
                PermissionNameList.StatisticalReport,
                PermissionNameList.StatisticalReportWrite,
                PermissionNameList.StatisticalReportCopy,
                PermissionNameList.StatisticalReportDelete,
                PermissionNameList.StatisticalReportExport,
                PermissionNameList.StatisticalReportLockAndUnlock,
                PermissionNameList.StatisticalReportPublish,
                PermissionNameList.StatisticalReportDataEntry
            }),
        new(PermissionNameList.StatisticalReportWrite, new[] { PermissionNameList.StatisticalReportCopy }),
        new(PermissionNameList.StatisticalReportCopy, new[] { PermissionNameList.StatisticalReport }),
        new(PermissionNameList.StatisticalReportDelete, new[] { PermissionNameList.StatisticalReport }),
        new(PermissionNameList.StatisticalReportExport, new[] { PermissionNameList.StatisticalReport }),
        new(PermissionNameList.StatisticalReportLockAndUnlock, new[] { PermissionNameList.StatisticalReport }),
        new(PermissionNameList.StatisticalReportPublish, new[] { PermissionNameList.StatisticalReport }),

        // Tournament
        new(PermissionNameList.Tournament, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.TournamentRead, new[] { PermissionNameList.Tournament }),


        // Capacity planning
        new(PermissionNameList.CapacityPlanning, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.CapacityPlanningRead, new[] { PermissionNameList.CapacityPlanningWrite,
            PermissionNameList.CapacityPlanningDelete }),
        new(PermissionNameList.CapacityPlanningWrite, new[] { PermissionNameList.CapacityPlanning }),
        new(PermissionNameList.CapacityPlanningDelete, new[] { PermissionNameList.CapacityPlanning }),

        // Other
        new(PermissionNameList.KpiResultTargetSettingMethod, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiResultCapabilityType, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiTag, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiType, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiBalancedBehaviorCard, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Policy, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.OperationRuleAndRegulation, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.OperationSpecification, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.CapabilityType, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Capability, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.OperationEnhancementType, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.PlanInput, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Team, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Service, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.ImprovementOpportunity, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.ImprovementOpportunityInputCategory, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.ImprovementOpportunityInputSource, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.Notification, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.StrategicGoal, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.GovernmentStrategicGoal, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.PlanCategory, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.ServiceCategory, new[] { PermissionNameList.FullAccess }),
        new(PermissionNameList.KpiResultCategory, new[] { PermissionNameList.FullAccess }),


    };

    // The list of permissions with the full list
    // of overriders (computed only once at run time).
    private static Permission[] _value;

    public static Permission[] Value
    {
        get
        {
            return _value ??=
                InitialList
                    .Select(x =>
                        new Permission(x.Id, GenerateAllOverridenIdList(x.Id)))
                    .ToArray();
        }
    }

    private static string[] GenerateAllOverridenIdList(string permissionId)
    {
        // Get the overriders of the permission
        var (_, overriders) = InitialList.First(x => x.Id.Equals(permissionId));

        // Get the overriders of the overriders.
        return overriders
            .Concat(
                overriders.SelectMany(GenerateAllOverridenIdList)
            )
            .Distinct()
            .ToArray();
    }
}
